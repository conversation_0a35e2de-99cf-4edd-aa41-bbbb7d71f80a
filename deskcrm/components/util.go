package components

import (
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"os"
	"path"
	"reflect"
	"regexp"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type util struct {
}

var Util util

const reg = `1[0-9]\d{9}`

var rgx = regexp.MustCompile(reg)

func (s util) GetShipEnvNameFromRemote(ctx *gin.Context) string {
	fmt.Printf("deskcrm:%+v\n", conf.API.ArkGo)
	res, err := conf.API.ArkGo.HttpGet(ctx, "/deskcrm/sys/shipenvname", base.HttpRequestOptions{})
	if err != nil {
		zlog.Infof(ctx, "http get err: %v", err)
		return ""
	}
	var data struct {
		Name string `json:"name"`
	}
	_ = jsoniter.Unmarshal(res.Response, &data)
	return data.Name
}

// GetEnvName 获取ship环境容器名 SHIP_ENV_NAME
func (s util) GetEnvName(ctx *gin.Context) (name string) {
	name = utils.GetEnvName(ctx)
	if name == "" {
		name = s.GetShipEnvNameFromRemote(ctx)
	}
	return name
}

func (s util) DownloadFile(ctx *gin.Context, fileUrl string) (fileName string, err error) {
	resp, err := http.Get(fileUrl)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return
	}

	fileName = path.Base(fileUrl)

	err = ioutil.WriteFile(fileName, data, 0644)
	if err != nil {
		return "", err
	}

	return fileName, nil
}

func (s util) ChunkArrayInt(array []int, size int) [][]int {
	if len(array) == 0 || size <= 0 {
		return nil
	}

	total := len(array) / size
	if len(array)%size != 0 {
		total++
	}

	result := make([][]int, 0, total)
	for i := 0; i < len(array); {
		stop := i + size
		if stop > len(array) {
			stop = len(array)
		}
		item := array[i:stop]
		result = append(result, item)

		i = stop
	}

	return result
}

func (s util) InArrayString(e string, array []string) bool {
	for _, element := range array {
		if e == element {
			return true
		}
	}

	return false
}

func (s util) FirstToUpper(str string) string {
	if str == "" {
		return str
	}
	return strings.ToUpper(str[:1]) + str[1:]
}

func (s util) WriteToCsv(file string, data [][]string) error {
	File, err := os.OpenFile(file, os.O_RDWR|os.O_CREATE, 0666)
	_, err = File.WriteString("\xEF\xBB\xBF") // excel需要utf-8 withbom格式
	if err != nil {
		return err
	}
	defer File.Close()
	var fErr error
	if fErr != nil {
		return fErr
	}
	WriterCsv := csv.NewWriter(File)
	for _, _d := range data {
		err1 := WriterCsv.Write(_d)
		if err1 != nil {
			return err1
		}
	}

	WriterCsv.Flush()
	return nil
}

func IsEmpty(value interface{}) bool {
	if value == nil {
		return true
	}

	v := reflect.ValueOf(value)

	switch v.Kind() {
	case reflect.Array, reflect.Chan, reflect.Map, reflect.Slice, reflect.String:
		return v.Len() == 0
	case reflect.Bool:
		return !v.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return v.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return v.Float() == 0
	case reflect.Struct:
		return false
	case reflect.Ptr:
		return v.IsNil()
	default:
		return false
	}
}

func GetCityLevel(level int) string {
	switch level {
	case 1:
		return "一线"
	case 2:
		return "准一线"
	case 3:
		return "二线"
	case 4:
		return "三线"
	case 5:
		return "四线"
	case 6:
		return "五线"
	case 255:
		return "其他"
	default:
		return "未知"
	}
}

func RoundToInt(num float64) int64 {
	rounded := math.Round(num)
	return int64(rounded)
}

func ExtractNumbers(input []string) ([]int64, error) {
	var numbers []int64
	for _, str := range input {
		parts := strings.Split(str, "_")
		if len(parts) < 3 {
			return nil, errors.New("input does not contain enough parts")
		}
		numStr := parts[2]
		num, err := strconv.ParseInt(numStr, 10, 64)
		if err != nil {
			return nil, err
		}
		numbers = append(numbers, num)
	}
	return numbers, nil
}

func ExtractString(input []string, moduleID int64) ([]string, error) {
	var keys []string
	for _, str := range input {
		keys = append(keys, strings.TrimPrefix(str, "ArkGo:"+fmt.Sprintf("ArkRuleDetail_%d_", moduleID)))
	}
	return keys, nil
}

func MergeMapsInt[T any](map1, map2 map[int64]T) map[int64]T {
	mergedMap := make(map[int64]T)

	for k, v := range map1 {
		mergedMap[k] = v
	}

	for k, v := range map2 {
		mergedMap[k] = v
	}

	return mergedMap
}

func MergeMapsString[T any](maps ...map[string]T) map[string]T {
	mergedMap := make(map[string]T)

	for _, mapValues := range maps {
		for k, v := range mapValues {
			mergedMap[k] = v
		}
	}

	return mergedMap
}

func MapValuesToPtrInt[T any](m map[int64]T) map[int64]*T {
	ptrMap := make(map[int64]*T)
	for k, v := range m {
		value := v
		ptrMap[k] = &value
	}
	return ptrMap
}

func MapValuesToPtrString[T any](m map[string]T) map[string]*T {
	ptrMap := make(map[string]*T)
	for k, v := range m {
		value := v
		ptrMap[k] = &value
	}
	return ptrMap
}

func IsDebugModel(ctx *gin.Context) bool {
	if ctx.Request == nil {
		return false
	}
	debugTag, _ := ctx.Cookie("debug_model")
	return debugTag == "1"
}

func (s util) RoutineRun(ctx *gin.Context, wg *sync.WaitGroup, fun func()) {
	go func() {
		defer func() {
			if errPanic := recover(); errPanic != nil {
				zlog.Errorf(ctx, "RoutineRun panic, err:%v", errPanic)
				Util.PanicTrace(ctx)
			}
		}()
		defer wg.Done()
		fun()
	}()
}

func (s util) HiddenPhone(str string) string {
	mobileMatch := rgx.MatchString(str)
	if mobileMatch { // 手机号
		return fwyyutils.Substr(str, 0, 3) + "****" + fwyyutils.Substr(str, 7, 4)
	}
	return str
}

func (s util) PanicTrace(ctx *gin.Context) {
	pc, file, line, _ := runtime.Caller(20) // 20 表示向上回溯 20 层
	stackInfo := string(debug.Stack())
	zlog.Errorf(ctx, "[GoWithRecoverAndReturnErr] the function catch panic err, panic occurred in %s[%s:%d], stackInfo: %s", runtime.FuncForPC(pc).Name(), file, line, stackInfo)
}

func (s util) SplitToInt64(data, split string) (values []int64) {
	values = make([]int64, 0)
	data = strings.TrimSpace(data)
	if data == "" {
		return
	}

	vs := strings.Split(data, split)
	for _, v := range vs {
		v = strings.TrimSpace(v)
		if v == "" {
			continue
		}

		e, err := cast.ToInt64E(v)
		if err != nil {
			continue
		}

		values = append(values, e)
	}
	return
}

func (s util) GetCustomKey(ctx *gin.Context, id, optionType int64) (key string) {
	zlog.Debugf(ctx, "getCustomKey id:%+v, optionType:%+v", id, optionType)
	return fmt.Sprintf("customKey_%d_%d", id, optionType)
}

func (s util) StudentMapToStudentList(ctx *gin.Context, studentMap map[int64]map[string]interface{}) (studentList []map[string]interface{}, err error) {
	zlog.Debugf(ctx, "studentMapToStudentList, studentMap:%+v", studentMap)
	//排序
	studentList = make([]map[string]interface{}, 0)
	for _, studentInfo := range studentMap {
		studentList = append(studentList, studentInfo)
	}
	return studentList, nil
}

func (s util) ContainMapKey(key string, m map[string]string) bool {
	if len(m) == 0 {
		return false
	}
	for k, _ := range m {
		if key == k {
			return true
		}
	}
	return false
}

func (s util) ConvertToString(ori []int64) []string {
	res := make([]string, 0)
	for _, item := range ori {
		res = append(res, strconv.FormatInt(item, 10))
	}
	return res
}

func (s util) FormatRemainTimeTSF(remainTime int64) string {
	if remainTime <= 0 {
		return "0分"
	}

	var str string
	day := remainTime / 86400
	if day > 0 {
		str += fmt.Sprintf("%d天", day)
	}

	hours := (remainTime % 86400) / 3600
	if hours > 0 {
		str += fmt.Sprintf("%d时", hours)
	}

	minutes := ((remainTime % 86400) % 3600) / 60
	if minutes > 0 {
		str += fmt.Sprintf("%d分", minutes)
	}

	if str == "" {
		return "0分"
	}
	return str
}

// FormatRemainTime 格式化剩余时间，对应PHP的AssistantDesk_Tools::formatRemainTime方法
func (s util) FormatRemainTime(remainTime int64, isSecond bool) string {
	if remainTime <= 0 {
		if isSecond {
			return "0秒"
		}
		return "0分钟"
	}

	var str string
	day := remainTime / 86400
	if day > 0 {
		str += fmt.Sprintf("%d天", day)
	}

	hours := (remainTime % 86400) / 3600
	if hours > 0 {
		str += fmt.Sprintf("%d小时", hours)
	}

	minutes := ((remainTime % 86400) % 3600) / 60
	if minutes > 0 {
		str += fmt.Sprintf("%d分钟", minutes)
	}

	if isSecond {
		seconds := ((remainTime % 86400) % 3600) % 60
		if seconds > 0 {
			str += fmt.Sprintf("%d秒", seconds)
		}

		if str == "" {
			return "0秒"
		}
	} else {
		if str == "" {
			return "0分钟"
		}
	}

	return str
}

func StructToMap(ctx *gin.Context, s interface{}) map[string]interface{} {
	// 将结构体序列化为 JSON 字符串
	jsonBytes, err := json.Marshal(s)
	if err != nil {
		zlog.Errorf(ctx, "StructToMap mashal, err:%v", err)
		return nil
	}

	var result map[string]interface{}
	err = json.Unmarshal(jsonBytes, &result)
	if err != nil {
		zlog.Errorf(ctx, "StructToMap unmashal, err:%v", err)
		return nil
	}

	return result
}
