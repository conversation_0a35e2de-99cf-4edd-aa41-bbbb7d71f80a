domainPrefix: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=

tower:
  service: tower
  domain: http://tower-svc.support:8080
  timeout: 1000ms
  httpStat: true
  retry: 1

jxnotice:
  service: jxnotice
  # 请求完整地址
  domain: http://jxnotice-svc.edu:8080
  timeout: 1500ms
  retry: 1

livestation:
  service: livestation
  # 请求完整地址
  domain: http://livestation-svc.edu:8080
  timeout: 1000ms
  retry: 1

assistantdesk:
  service: assistantdesk
  domain: http://assistantdesk-svc.support:8080
  timeout: 6000ms
  retry: 1

moat:
  service: moat
  domain: http://moat2-svc.sell-gateway:8080
  timeout: 1000ms
  retry: 1

dataproxy:
  service: dataproxy
  domain: http://dataproxy-svc.support:8080
  timeout: 1000ms
  retry: 1

zbcoredal:
  service: zbcoredal
  domain: http://dal-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredat:
  service: zbcoredat
  domain: http://dat-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredau:
  service: zbcoredau
  domain: http://dau-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredas:
  service: zbcoredas
  domain: http://das-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredar:
  service: zbcoredar
  domain: http://moat2-svc.sell-gateway:8080
  timeout: 1500ms
  httpStat: true
  retry: 1
  moatAppKey: assistantdesk
  moatAppSecret: 86ec2d3211ac6e7ff5fcbd95fbf4e2c0

longservice:
  service: longservice
  domain: http://10.116.252.48:8090
  timeout: 1000ms
  httpStat: true
  retry: 1

mercury:
  service: mercury
  domain: http://mercury-svc.edu:8080
  timeout: 3000ms
  retry: 1

mesh:
  service: mesh
  domain: http://fwyy-mesh-svc.support:8080
  appkey: 68DA5D3D7F2859B0
  timeout: 1500ms
  retry: 1

allocate:
  service: allocate
  domain: http://allocate-svc.lpc:8080
  timeout: 1000ms
  httpStat: true
  retry: 1

assistantdeskgo:
  service: assistantdeskgo
  domain: http://assistantdeskgo-svc.support:8080
  timeout: 3000ms
  retry: 3

arkgo:
  service: arkgo
  domain: http://arkgo-svc.support:8080
  timeout: 3000ms
  retry: 3

genke:
  service: genke
  domain: http://genke-svc.support:8080
  timeout: 3000ms
  retry: 3

kpstaff:
  service: kpstaff
  domain: http://kpstaff-svc.kunpeng:8080
  timeout: 2000ms
  retry: 2

coursetransgo:
  service: coursetransgo
  domain: http://coursetransgo-svc.lpc:8080
  timeout: 2000ms
  retry: 3

jxreport:
  service: jxreport
  domain: http://jxreport-svc.edu:8080
  timeout: 3000ms
  retry: 1

jwbiz:
  service: jwbiz
  domain: http://jwbiz-svc.sell-course:8080
  timeout: 3000ms
  retry: 1

userprofile:
  service: userprofile
  domain: http://userprofile-svc.support:8080
  timeout: 3000ms
  retry: 2

examcore:
  service: examcore
  domain: http://examcore-svc.edu:8080
  timeout: 3000ms
  retry: 1

dalgo:
  service: dalgo
  domain: http://dalgo-svc.sell-course:8080
  timeout: 2500ms
  httpStat: true
  retry: 1

kpapi:
  service: kpapi
  domain: http://kpapi-svc.kunpeng:8080
  timeout: 2000ms
  retry: 1

lpcduxuesc:
  service: lpcduxuesc
  domain: http://duxuesc-svc.lpc:8080
  timeout: 5000ms
  retry: 1

learningplan:
  service: learningplan
  domain: http://learnplan-go-svc.edu:8080
  timeout: 3000ms
  httpStat: true
  retry: 1

achilles-v3:
  service: achilles-v3
  domain: http://achilles-v3-server-svc.edu:8080
  timeout: 3000ms
  httpStat: true
  retry: 1

tag:
  service: tag
  domain: http://tag-svc.support-xieyi:8080
  timeout: 3000ms
  retry: 1

muse:
  service: muse
  domain: http://muse-svc.support:8080
  timeout: 3000ms
  retry: 1

touchmisgo:
  service: touchmisgo
  domain: http://touchmisgo-svc.support:8080
  timeout: 3000ms
  retry: 1

touchmis:
  service: touchmis
  domain: http://touchmis-svc.lpc:8080
  timeout: 3000ms
  retry: 1

location:
  service: location
  domain: http://location-svc.inf:8080
  timeout: 3000ms
  retry: 1

frontcoursenew:
  service: frontcoursenew
  domain: http://frontcourse-svc.edu-front:8080
  timeout: 3000ms
  retry: 1

jxexamui:
  service: jxexamui
  domain: http://jxexamui-svc.edu-front:8080
  timeout: 3000ms
  retry: 1

pcassistant:
  service: pcassistant
  domain: http://pcassistant-svc.support:8080
  timeout: 3000ms
  retry: 1

jxdascore:
  service: jxdascore
  domain: http://jxda-score-svc.edu:8080
  timeout: 3000ms
  retry: 1

writereport:
  service: writereport
  domain: http://writereport-svc.deer:8080
  timeout: 3000ms
  retry: 1

# 本地deskcrm服务API客户端
deskcrm:
  service: deskcrm
  domain: http://127.0.0.1:8010
  timeout: 3000ms
  retry: 1

coursebase:
  service: coursebase
  domain: http://sellmis-base-cc.suanshubang.cc
  timeout: 3000ms
  retry: 1

