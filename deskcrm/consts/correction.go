package consts

// 订正相关常量和映射表，对应PHP中的Hkzb_Const_Correction

// GetStageByGrade 根据年级计算学部，对应PHP中的Zb_Const_GradeSubject::$GRADEMAPXB
func GetStageByGrade(grade int64) int64 {
	switch grade {
	// 小学(1)
	case 1, 11, 12, 13, 14, 15, 16:
		return 1
	// 初中(20)
	case 2, 3, 4, 20, 21:
		return 20
	// 高中(30)
	case 5, 6, 7, 30, 31, 32, 33, 92:
		return 30
	// 学前(60)
	case 60, 61, 62, 63, 64:
		return 60
	// 成人(70)
	case 70, 71:
		return 70
	// 大学(80)
	case 81, 82, 83, 84, 100:
		return 80
	// 低幼(90)
	case 90, 91:
		return 90
	default:
		return 0 // 对应PHP中的false，表示未识别的年级
	}
}

// ShortHomeworkTypeGradeSubjectMap 短作业类型年级科目映射
var ShortHomeworkTypeGradeSubjectMap = map[int64]map[int64]map[int64]int64{
	60: { // 学前
		61: { // 小班
			1: 2, // 语文，订正
			2: 2, // 数学，订正
			3: 2, // 英语，订正
		},
		62: { // 中班
			1: 2, // 语文，订正
			2: 2, // 数学，订正
			3: 2, // 英语，订正
		},
		63: { // 大班
			1: 2, // 语文，订正
			2: 2, // 数学，订正
			3: 2, // 英语，订正
		},
		64: { // 学前班
			1: 2, // 语文，订正
			2: 2, // 数学，订正
			3: 2, // 英语，订正
		},
	},
	1: { // 小学
		11: { // 一年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养,订正
			101: 2, // 剑桥英语,订正
		},
		12: { // 二年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养,订正
			101: 2, // 剑桥英语,订正
		},
		13: { // 三年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养,订正
			101: 2, // 剑桥英语,订正
		},
		14: { // 四年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养,订正
			101: 2, // 剑桥英语,订正
		},
		15: { // 五年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养,订正
			101: 2, // 剑桥英语,订正
		},
		16: { // 六年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养,订正
			101: 2, // 剑桥英语,订正
		},
	},
	20: { // 初中
		2: { // 初一
			1:  2, // 语文
			2:  2, // 数学
			3:  2, // 英语
			4:  2, // 物理
			5:  2, // 化学
			12: 2, // 讲座
			16: 2, // 科学
			48: 2, // 道德与法治,订正
			55: 2, // 逻辑力，订正
			57: 2, // 阅读力，订正
			78: 2, // 人文素养,订正
			84: 2, // 科学,订正
			85: 2, // 人文创作，同语文
			86: 2, // 数理思维，同数学
			87: 2, // 双语素养，同英语
			88: 2, // 自然科学，同物理
			89: 2, // 实验科学，同化学
			91: 2, // 应用科学，同科学（订正）
			92: 2, // 基础科学，同科学
			90: 2, // 道法，同道德与法治
		},
		3: { // 初二
			1:  2, // 语文
			2:  2, // 数学
			3:  2, // 英语
			4:  2, // 物理
			5:  2, // 化学
			12: 2, // 讲座
			16: 2, // 科学
			48: 2, // 道德与法治,订正
			55: 2, // 逻辑力，订正
			57: 2, // 阅读力，订正
			78: 2, // 人文素养,订正
			84: 2, // 科学,订正
			85: 2, // 人文创作，同语文
			86: 2, // 数理思维，同数学
			87: 2, // 双语素养，同英语
			88: 2, // 自然科学，同物理
			89: 2, // 实验科学，同化学
			91: 2, // 应用科学，同科学（订正）
			92: 2, // 基础科学，同科学
			90: 2, // 道法，同道德与法治
		},
		4: { // 初三
			1:  2, // 语文
			2:  2, // 数学
			3:  2, // 英语
			4:  2, // 物理
			5:  2, // 化学
			12: 2, // 讲座
			16: 2, // 科学
			48: 2, // 道德与法治,订正
			55: 2, // 逻辑力，订正
			57: 2, // 阅读力，订正
			78: 2, // 人文素养,订正
			84: 2, // 科学,订正
			85: 2, // 人文创作，同语文
			86: 2, // 数理思维，同数学
			87: 2, // 双语素养，同英语
			88: 2, // 自然科学，同物理
			89: 2, // 实验科学，同化学
			91: 2, // 应用科学，同科学（订正）
			92: 2, // 基础科学，同科学
			90: 2, // 道法，同道德与法治
		},
	},
	30: { // 高中
		5: { // 高一
			1: 1, // 修改语文，老批改
			2: 1, // 修改数学，老批改
			3: 1, // 修改英语，老批改
			4: 1, // 修改物理，老批改
			5: 1, // 修改化学，老批改
			6: 1, // 修改生物，老批改
		},
		6: { // 高二
			1: 2, // 语文,只批改不订正，提交次数为1
			2: 2, // 数学,只批改不订正，提交次数为1
			3: 3, // 英语,只批改不订正，提交次数为1
			4: 3, // 物理,只批改不订正，提交次数为1
			5: 3, // 化学,只批改不订正，提交次数为1
			6: 2, // 生物,只批改不订正，提交次数为1
		},
		7: { // 高三
			1: 2, // 修改语文，短训班订正
			2: 1, // 数学,老巩固练习
			3: 1, // 英语,老巩固练习
			4: 1, // 物理,老巩固练习
			5: 1, // 化学,老巩固练习
			6: 1, // 生物,老巩固练习
		},
	},
}

// HomeworkTypeGradeSubjectMap 作业类型年级科目映射
var HomeworkTypeGradeSubjectMap = map[int64]map[int64]map[int64]int64{
	60: { // 学前
		61: { // 小班
			1: 2, // 语文，订正
			2: 2, // 数学，订正
			3: 2, // 英语，订正
		},
		62: { // 中班
			1: 2, // 语文，订正
			2: 2, // 数学，订正
			3: 2, // 英语，订正
		},
		63: { // 大班
			1: 2, // 语文，订正
			2: 2, // 数学，订正
			3: 2, // 英语，订正
		},
		64: { // 学前班
			1: 2, // 语文，订正
			2: 2, // 数学，订正
			3: 2, // 英语，订正
		},
	},
	1: { // 小学
		11: { // 一年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养，订正
			101: 2, // 剑桥英语,订正
		},
		12: { // 二年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养，订正
			101: 2, // 剑桥英语,订正
		},
		13: { // 三年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养，订正
			101: 2, // 剑桥英语,订正
		},
		14: { // 四年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养，订正
			101: 2, // 剑桥英语,订正
		},
		15: { // 五年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养，订正
			101: 2, // 剑桥英语,订正
		},
		16: { // 六年级
			1:   2, // 语文，订正
			2:   2, // 数学，订正
			3:   2, // 英语，订正
			12:  2, // 讲座
			33:  2, // 思维，订正
			47:  2, // 口才，订正
			53:  2, // 素养，订正
			54:  2, // 记忆力，订正
			55:  2, // 逻辑力，订正
			56:  2, // 专注力，订正
			57:  2, // 阅读力，订正
			58:  2, // 围棋，订正
			66:  2, // 人文，订正
			67:  2, // 语言，订正
			68:  2, // 家庭教育，订正
			69:  2, // 学习力，订正
			78:  2, // 人文素养，订正
			101: 2, // 剑桥英语,订正
		},
	},
	20: { // 初中
		2: { // 初一
			1:  2, // 语文
			2:  2, // 数学
			3:  2, // 英语
			4:  2, // 物理
			5:  2, // 化学
			12: 2, // 讲座
			16: 2, // 科学
			48: 2, // 道德与法治,订正
			55: 2, // 逻辑力，订正
			57: 2, // 阅读力，订正
			78: 2, // 人文素养,订正
			84: 2, // 科学,订正
			85: 2, // 人文创作，同语文
			86: 2, // 数理思维，同数学
			87: 2, // 双语素养，同英语
			88: 2, // 自然科学，同物理
			89: 2, // 实验科学，同化学
			91: 2, // 应用科学，同科学（订正）
			92: 2, // 基础科学，同科学
			90: 2, // 道法，同道德与法治
		},
		3: { // 初二
			1:  2, // 语文
			2:  2, // 数学
			3:  2, // 英语
			4:  2, // 物理
			5:  2, // 化学
			12: 2, // 讲座
			16: 2, // 科学
			48: 2, // 道德与法治,订正
			55: 2, // 逻辑力，订正
			57: 2, // 阅读力，订正
			78: 2, // 人文素养,订正
			84: 2, // 科学,订正
			85: 2, // 人文创作，同语文
			86: 2, // 数理思维，同数学
			87: 2, // 双语素养，同英语
			88: 2, // 自然科学，同物理
			89: 2, // 实验科学，同化学
			91: 2, // 应用科学，同科学（订正）
			92: 2, // 基础科学，同科学
			90: 2, // 道法，同道德与法治
		},
		4: { // 初三
			1:  2, // 语文
			2:  2, // 数学
			3:  2, // 英语
			4:  2, // 物理
			5:  2, // 化学
			12: 2, // 讲座
			16: 2, // 科学
			48: 2, // 道德与法治,订正
			55: 2, // 逻辑力，订正
			57: 2, // 阅读力，订正
			78: 2, // 人文素养,订正
			84: 2, // 科学,订正
			85: 2, // 人文创作，同语文
			86: 2, // 数理思维，同数学
			87: 2, // 双语素养，同英语
			88: 2, // 自然科学，同物理
			89: 2, // 实验科学，同化学
			91: 2, // 应用科学，同科学（订正）
			92: 2, // 基础科学，同科学
			90: 2, // 道法，同道德与法治
		},
	},
	30: { // 高中
		5: { // 高一
			1: 3, // 语文,只批改不做相似题
			2: 4, // 数学,做相似题
			3: 3, // 英语,只批改不做相似题
			4: 4, // 物理,修改做相似题
			5: 4, // 化学,修改做相似题
			6: 4, // 生物,修改做相似题
		},
		6: { // 高二
			1: 3, // 语文,修改为只批改不做相似题
			2: 4, // 数学,修改做相似题
			3: 3, // 英语,只批改不订正，提交次数为1
			4: 4, // 物理,修改做相似题
			5: 4, // 化学,修改做相似题
			6: 4, // 生物,修改做相似题
		},
		7: { // 高三
			1: 1, // 语文,老巩固联系
			2: 1, // 数学,老巩固练习
			3: 1, // 英语,老巩固练习
			4: 1, // 物理,老巩固练习
			5: 1, // 化学,老巩固练习
			6: 1, // 生物,老巩固练习
		},
	},
}

// 辅助函数：检查int64数组中是否包含某个值
func containsInt64(slice []int64, item int64) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

// GetHomeworkType 获取作业类型，对应PHP中的Hkzb_Util_Fudao_Correction::getHomeworkType
func GetHomeworkType(gradeId, subjectId, year int64, learnSeason string, courseStartTime, courseId, newCourseType int64) int64 {
	// 学季，取第一个字符
	var season int
	if len(learnSeason) > 0 {
		season = int(learnSeason[0] - '0')
		if season < 1 || season > 4 {
			season = 0
		}
	}

	// 学部
	stage := GetStageByGrade(gradeId)

	// 2023年巩固练习订正补丁-从此以后高中不再使用相似题模式
	if stage == 30 {
		return HomeworkTypeOld
	}

	// 标准配置
	if year >= 2021 && season >= 1 {
		var homeworkType int64
		if newCourseType == 12 { // 短训班
			if stageMap, exists := ShortHomeworkTypeGradeSubjectMap[stage]; exists {
				if gradeMap, exists := stageMap[gradeId]; exists {
					if hwType, exists := gradeMap[subjectId]; exists {
						homeworkType = hwType
					}
				}
			}
		} else { // 非短训班
			if stageMap, exists := HomeworkTypeGradeSubjectMap[stage]; exists {
				if gradeMap, exists := stageMap[gradeId]; exists {
					if hwType, exists := gradeMap[subjectId]; exists {
						homeworkType = hwType
					}
				}
			}
		}
		if homeworkType > 0 {
			return homeworkType
		}
		return 1 // 默认返回1
	}

	// 非法的gradeId
	validGrades := []int64{2, 3, 4, 5, 6, 7, 11, 12, 13, 14, 15, 16, 61, 62, 63, 64}
	if !containsInt64(validGrades, gradeId) {
		return HomeworkTypeOld
	}

	// 短训班高三处理
	if newCourseType == 12 && gradeId == 7 {
		if subjectId == 1 {
			return HomeworkTypeNewCorrection
		} else {
			return HomeworkTypeOld
		}
	}

	// 短训班高一
	if newCourseType == 12 && gradeId == 5 {
		return HomeworkTypeOld
	}

	// 默认从映射表获取
	if stageMap, exists := HomeworkTypeGradeSubjectMap[stage]; exists {
		if gradeMap, exists := stageMap[gradeId]; exists {
			if hwType, exists := gradeMap[subjectId]; exists {
				return hwType
			}
		}
	}

	return HomeworkTypeOld // 默认返回老批改系统
}
