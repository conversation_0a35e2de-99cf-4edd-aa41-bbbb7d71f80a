package outputStudent

import "git.zuoyebang.cc/fwyybase/fwyylibs/api/assistantdeskgo"

type StudentDetailV1Resp struct {
	Student            StudentInfo `json:"student"`
	ContactFlagDefault string      `json:"contactFlagDefault"`
	ContactFlagTime    string      `json:"contactFlagTime"`
	AssistantUid       int64       `json:"assistantUid"`
	AssistantPhone     string      `json:"assistantPhone"`
	Tags               []Tag       `json:"tags"`
	IsLevelTwo         int         `json:"isLevelTwo"`
}

type StudentInfo struct {
	StudentUid          int64                          `json:"studentUid"`
	LeadsId             int64                          `json:"leadsId"`
	EncryptedStudentUid string                         `json:"encryptedStudentUid"`
	StudentName         string                         `json:"studentName"`
	Nickname            string                         `json:"nickname"`
	Sex                 int                            `json:"sex"`
	Avatar              string                         `json:"avatar"`
	Grade               string                         `json:"grade"`
	Phone               string                         `json:"phone"`
	Address             interface{}                    `json:"address"`
	Guardian            string                         `json:"guardian"`
	GuardianPhone       string                         `json:"guardianPhone"`
	GuardianWechat      string                         `json:"guardianWechat"`
	RegPhone            string                         `json:"regPhone"`
	RegTime             string                         `json:"regTime"`
	Area                string                         `json:"area"`
	School              string                         `json:"school"`
	ClassOL             string                         `json:"classOL"`
	GuardianWechatLight int                            `json:"guardianWechatLight"`
	AddressPhone        string                         `json:"addressPhone"`
	ServiceType         int                            `json:"serviceType"`
	ClassId             int                            `json:"classId"`
	ScRemark            string                         `json:"scRemark"`
	PreContinue         int                            `json:"preContinue"`
	MachinePreContinue  int                            `json:"machinePreContinue"`
	GradeData           string                         `json:"gradeData"`
	EncryCustomUid      string                         `json:"encryCustomUid"`
	Md5AddressPhone     string                         `json:"md5AddressPhone"`
	Md5GuardianPhone    string                         `json:"md5GuardianPhone"`
	Md5Phone            string                         `json:"md5Phone"`
	Md5RegPhone         string                         `json:"md5RegPhone"`
	StudentNamePinYin   assistantdeskgo.GetPingYinItem `json:"studentNamePinYin"`
}

type Tag struct {
	Label       string `json:"label"`
	Color       string `json:"color"`
	BorderColor string `json:"borderColor,omitempty" ` // for bai lin
	Cname       string `json:"cname"`
	Hover       string `json:"hover"`
	Prop        string `json:"prop,omitempty"`
}
