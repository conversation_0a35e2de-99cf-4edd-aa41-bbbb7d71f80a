package course

import (
	"deskcrm/api/dal"
	"deskcrm/api/moat"
	"deskcrm/components"
	"deskcrm/components/define"
	"deskcrm/consts"
	"deskcrm/models"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/stru/course"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/allocate"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/coursebase"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sync"
)

type courseDataGet struct {
	BaseCourseInfo map[int64]dal.CourseInfo
}

func (c courseDataGet) GetCourseSurveyNetInfo(ctx *gin.Context, assistantUid int64, courseIds []int64) (map[int64]course.SurveyTypeAndId, error) {
	surveyData := make(map[int64]course.SurveyTypeAndId)

	res, err := models.TblBindInfoDao.GetBindInfoByCourseIds(ctx, courseIds)
	if err != nil {
		return surveyData, err
	}
	zlog.Infof(ctx, "GetCourseSurveyNetInfo GetBindInfoByCourseIds,%v,%v", len(courseIds), len(res))
	for _, item := range res {
		courseId := item.CourseID
		if item.SurveyType == models.SurveyTypeWaxu {
			data, ok := surveyData[courseId]
			if !ok {
				surveyData[courseId] = course.SurveyTypeAndId{
					NeedSurveyId: cast.ToInt64(item.SurveyID),
					SurveyType:   []int64{item.SurveyType},
				}
			} else {
				surveyData[courseId] = course.SurveyTypeAndId{
					NeedSurveyId: cast.ToInt64(item.SurveyID),
					SurveyType:   append(data.SurveyType, item.SurveyType),
				}
			}
		}

		if item.SurveyType == models.SurveyTypeWenjuan {
			data, ok := surveyData[courseId]
			if !ok {
				surveyData[courseId] = course.SurveyTypeAndId{
					OrderSurveyId: cast.ToInt64(item.SurveyID),
					SurveyType:    []int64{item.SurveyType},
				}
			} else {
				surveyData[courseId] = course.SurveyTypeAndId{
					OrderSurveyId: cast.ToInt64(item.SurveyID),
					SurveyType:    append(data.SurveyType, item.SurveyType),
				}
			}
		}
	}
	return surveyData, nil
}

func (c courseDataGet) GetNormalLeadsCountByBatchCourseAssistant(ctx *gin.Context, assistantUid int64, courseIds []int64) (map[int64]allocate.CourseAssistantLeadsCount, error) {
	res := make(map[int64]allocate.CourseAssistantLeadsCount)
	countInfo, err := allocate.GetNormalLeadsCountByBatchCourseAssistant(ctx, courseIds, assistantUid)
	if err != nil {
		return nil, err
	}

	for _, item := range countInfo {
		res[int64(item.CourseID)] = item
	}
	return res, nil
}

func (c courseDataGet) GetCourseInfoList(ctx *gin.Context, courseIds []int64) (map[int64]tower.GetCourseInfoRsp, error) {
	res := make(map[int64]tower.GetCourseInfoRsp)
	transInfo, err := tower.GetCourseInfoList(ctx, courseIds)
	if err != nil {
		return nil, err
	}

	if transInfo == nil {
		zlog.Warnf(ctx, "GetCourseInfoList no data,%v", courseIds)
		return nil, nil
	}

	for _, item := range *transInfo {
		res[item.CourseID] = item
	}
	return res, nil
}

func (c courseDataGet) GeTransTimeStartByCourseIds(ctx *gin.Context, courseIds []int64) (map[int64]tower.GetTransTimeStartByCourseIdsRspItem, error) {
	res := make(map[int64]tower.GetTransTimeStartByCourseIdsRspItem)

	chunk := components.Array.ChunkArrayInt64(courseIds, consts.TowerAPIBatchSize)
	for _, ids := range chunk {
		transInfo, err := tower.GeTransTimeStartByCourseIds(ctx, ids)
		if err != nil {
			return nil, err
		}

		for _, item := range transInfo.List {
			res[item.CourseID] = item
		}
	}

	return res, nil
}

func (c courseDataGet) GetBatchExpireTimeByCourseList(ctx *gin.Context, courseIds []int64) (map[int64]tower.CourseExpireTime, error) {
	res := make(map[int64]tower.CourseExpireTime)
	expireInfo, err := tower.GetBatchExpireTimeByCourseIds(ctx, courseIds)
	if err != nil {
		return nil, err
	}

	for _, item := range expireInfo {
		res[item.CourseId] = item
	}
	return res, nil
}

func (c courseDataGet) GetSkuBaseInfoByCourseIds(ctx *gin.Context, courseIds []int64) (map[int64]moat.FormatSkuInfos, error) {
	// 获取 sku id
	skuIdMap, err := GetSkuIdByCourseIds(ctx, courseIds)
	if err != nil {
		return nil, err
	}

	// 获取sku 信息
	skuIds := make([]int64, 0)
	for _, skuId := range skuIdMap {
		skuIds = append(skuIds, skuId)
	}

	skuInfos := make(map[int64]moat.SkuInfos)
	subjectMap := make(map[int64]string)
	seasonName := make(map[int]string)

	wg := &sync.WaitGroup{}
	chErr := make(chan error, 3)
	wg.Add(3)
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		skuInfos, err = GetSkuInfoBySkuIds(ctx, skuIds)
		if err != nil {
			return err
		}
		return nil
	}, chErr)
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		// 学科名称
		subjectMap, err = dataQuery.New().GetSubjectIdNameMap(ctx)
		if err != nil {
			return err
		}
		return nil
	}, chErr)
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		seasonName = coursebase.GetLearnSeasonIdNameFullMap(ctx)
		if len(seasonName) == 0 {
			seasonName = define.LearnSeasonNameMap
		}
		return nil
	}, chErr)

	wg.Wait()
	close(chErr)

	for errInfo := range chErr {
		if errInfo != nil {
			return nil, errInfo
		}
	}

	// format sku 信息
	formatSkuInfo, err := FormatSkuInfo(ctx, skuInfos, subjectMap, seasonName)
	if err != nil {
		return nil, err
	}

	return formatSkuInfo, nil
}

func (c courseDataGet) GetBaseInfoByCourseIds(ctx *gin.Context, courseIds []int64) (map[int64]dal.CourseInfo, error) {
	res := make(map[int64]dal.CourseInfo)
	courseInfos, err := dal.GetCourseLessonInfoByCourseIds(ctx, courseIds)
	if err != nil {
		return nil, err
	}
	for courseId, info := range courseInfos {
		res[cast.ToInt64(courseId)] = info
	}
	return res, nil
}
