package ui

import (
	"deskcrm/api/dal"
	"deskcrm/api/genke"
	"deskcrm/api/moat"
	"deskcrm/api/tower"
	"deskcrm/components"
	"deskcrm/components/define"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/output/outputCourse"
	"deskcrm/libs/utils"
	"deskcrm/service/arkBase/dataQuery"
	"deskcrm/service/innerapi/course"
	courseStru "deskcrm/stru/course"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/allocate"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/coursebase"
	"git.zuoyebang.cc/fwyybase/fwyylibs/api/touchmis"
	baseTower "git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"sort"
	"strings"
	"sync"
	"time"
)

var CourseService courseService

type courseService struct {
}

func (s courseService) CourseListAndCardByYear(ctx *gin.Context, assistantUid, year int64) (resp outputCourse.CourseListAndCardByYearResp, err error) {
	defer func() {
		if err != nil {
			zlog.Errorf(ctx, "CourseListAndCardByYear error, err:%v", err)
		}

		if errPanic := recover(); errPanic != nil {
			zlog.Errorf(ctx, "CourseListAndCardByYear panic, err:%v", errPanic)
			components.Util.PanicTrace(ctx)
		}
	}()

	resp = outputCourse.CourseListAndCardByYearResp{CourseList: make([]outputCourse.CourseListAndCardByYearItem, 0)}

	// 获取老师下的所有课程信息
	courseIds := make([]int64, 0)
	courses, err := tower.NewClient().GetCourseBindByDeviceUid(ctx, []int64{assistantUid}, year)
	if err != nil {
		return
	}
	for id, _ := range courses {
		courseIds = append(courseIds, cast.ToInt64(id))
	}
	if len(courseIds) == 0 {
		return
	}

	components.Debugf(ctx, "CourseListAndCardByYear GetCourseBindByDeviceUid,%v,%v,%v", assistantUid, year, courseIds)

	// 获取需要的相关数据
	courseData, err := s.initDataStepOne(ctx, assistantUid, courseIds)
	if err != nil {
		return
	}
	courseData, err = s.initDataStepTwo(ctx, assistantUid, courseIds, courseData)
	if err != nil {
		return
	}

	// format 返回结果
	resp, err = s.formatCourseCardInfo(ctx, assistantUid, year, courseIds, courseData)
	if err != nil {
		return
	}

	return
}

type courseListAndCardByYearDataCollect struct {
	baseCourseInfo      map[int64]dal.CourseInfo
	courseSkuFormatInfo map[int64]moat.FormatSkuInfos
	expireInfo          map[int64]baseTower.CourseExpireTime
	transTimeInfo       map[int64]baseTower.GetTransTimeStartByCourseIdsRspItem
	towerCourseInfo     map[int64]baseTower.GetCourseInfoRsp
	leadsCountInfo      map[int64]allocate.CourseAssistantLeadsCount
	courseSurveyNetInfo map[int64]courseStru.SurveyTypeAndId
	nextAndLastLesson   map[int64]courseStru.NextLastLessonStruct
	stageMap            map[int64]int64
	subjectMap          map[int64]string
	lock                *sync.Mutex
}

func (s courseService) initDataStepOne(ctx *gin.Context, assistantUid int64, courseIds []int64) (*courseListAndCardByYearDataCollect, error) {
	task := &courseListAndCardByYearDataCollect{
		lock: &sync.Mutex{},
	}
	wg := &sync.WaitGroup{}

	chErr := make(chan error, 9)
	wg.Add(9)
	// course base info
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		courseBaseInfo, err := course.GetCourseDataInstance(ctx).GetBaseInfoByCourseIds(ctx, courseIds)
		if err != nil {
			components.Debugf(ctx, "CourseListAndCardByYear GetBaseInfoByCourseIds err,%v,%+v", courseIds, err)
			return err
		}
		task.lock.Lock()
		defer task.lock.Unlock()
		task.baseCourseInfo = courseBaseInfo
		return nil
	}, chErr)

	// sku format info
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		courseSkuFormatInfo, err := course.GetCourseDataInstance(ctx).GetSkuBaseInfoByCourseIds(ctx, courseIds)
		if err != nil {
			components.Debugf(ctx, "CourseListAndCardByYear GetSkuBaseInfoByCourseIds err,%v,%+v", courseIds, err)
			return err
		}
		task.lock.Lock()
		defer task.lock.Unlock()
		task.courseSkuFormatInfo = courseSkuFormatInfo
		return nil
	}, chErr)

	// 有效期信息
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		expireInfo, err := course.GetCourseDataInstance(ctx).GetBatchExpireTimeByCourseList(ctx, courseIds)
		if err != nil {
			components.Debugf(ctx, "CourseListAndCardByYear GetBatchExpireTimeByCourseList err,%v,%+v", courseIds, err)
			return err
		}
		task.lock.Lock()
		defer task.lock.Unlock()
		task.expireInfo = expireInfo
		return nil
	}, chErr)

	// 转化期信息
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		courseTransInfo, err := course.GetCourseDataInstance(ctx).GeTransTimeStartByCourseIds(ctx, courseIds)
		if err != nil {
			components.Debugf(ctx, "CourseListAndCardByYear GeTransTimeStartByCourseIds err,%v,%+v", courseIds, err)
			return err
		}
		task.lock.Lock()
		defer task.lock.Unlock()
		task.transTimeInfo = courseTransInfo
		return nil
	}, chErr)

	// tower 课程基本信息
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		towerCourseInfo, err := course.GetCourseDataInstance(ctx).GetCourseInfoList(ctx, courseIds)
		if err != nil {
			components.Debugf(ctx, "CourseListAndCardByYear GetCourseInfoList err,%v,%+v", courseIds, err)
			return err
		}
		task.lock.Lock()
		defer task.lock.Unlock()
		task.towerCourseInfo = towerCourseInfo
		return nil
	}, chErr)

	// 班级学生人数信息
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		leadsCountInfo, err := course.GetCourseDataInstance(ctx).GetNormalLeadsCountByBatchCourseAssistant(ctx, assistantUid, courseIds)
		if err != nil {
			components.Debugf(ctx, "CourseListAndCardByYear GetNormalLeadsCountByBatchCourseAssistant err,%v,%+v", courseIds, err)
			return err
		}
		task.lock.Lock()
		defer task.lock.Unlock()
		task.leadsCountInfo = leadsCountInfo
		return nil
	}, chErr)

	// 问卷信息获取
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		courseSurveyNetInfo, err := course.GetCourseDataInstance(ctx).GetCourseSurveyNetInfo(ctx, assistantUid, courseIds)
		if err != nil {
			components.Debugf(ctx, "CourseListAndCardByYear GetCourseSurveyNetInfo err,%v,%+v", courseIds, err)
			return err
		}
		task.lock.Lock()
		defer task.lock.Unlock()
		task.courseSurveyNetInfo = courseSurveyNetInfo
		return nil
	}, chErr)

	// key 值管理
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		// 年级关联学部
		stageMap, err := dataQuery.New().GetGradeToStage(ctx)
		if err != nil {
			components.Debugf(ctx, "CourseListAndCardByYear GetGradeToStage err,%v,%+v", courseIds, err)
			return err
		}
		task.lock.Lock()
		defer task.lock.Unlock()
		task.stageMap = stageMap
		return nil
	}, chErr)

	// key 值管理
	fwyyutils.GoWithRecoverAndReturnErr(nil, func() error {
		defer wg.Done()
		// 学科名称
		subjectMap, err := dataQuery.New().GetSubjectIdNameMap(ctx)
		if err != nil {
			components.Debugf(ctx, "CourseListAndCardByYear GetSubjectIdNameMap err,%v,%+v", courseIds, err)
			return err
		}
		task.lock.Lock()
		defer task.lock.Unlock()
		task.subjectMap = subjectMap
		return nil
	}, chErr)

	wg.Wait()
	close(chErr)

	for errInfo := range chErr {
		if errInfo != nil {
			return nil, errInfo
		}
	}

	return task, nil
}

func (s courseService) initDataStepTwo(ctx *gin.Context, assistantUid int64, courseIds []int64, courseData *courseListAndCardByYearDataCollect) (*courseListAndCardByYearDataCollect, error) {
	if courseData == nil {
		return courseData, nil
	}

	resData := make(map[int64]courseStru.NextLastLessonStruct)

	lessonIds := make([]int, 0)
	lessonListArr := make(map[int64][]dal.LessonInfo)
	// 获取章节信息
	for courseId, courseInfo := range courseData.baseCourseInfo {
		if _, ok := lessonListArr[courseId]; !ok {
			lessonListArr[courseId] = make([]dal.LessonInfo, 0)
		}
		for lessonId, info := range courseInfo.LessonList {
			lessonListArr[courseId] = append(lessonListArr[courseId], info)
			lessonIds = append(lessonIds, cast.ToInt(lessonId))
		}
	}

	lessonIds = components.Array.UniqueInt(lessonIds)

	for courseId, _ := range courseData.baseCourseInfo {
		// 排序
		lessonArr := lessonListArr[courseId]
		sort.SliceStable(lessonArr, func(i, j int) bool {
			return lessonArr[i].StartTime < lessonArr[j].StartTime
		})

		tmplData := courseStru.NextLastLessonStruct{}
		for _, lessonDetail := range lessonArr {
			currentTime := time.Now().Unix()

			tmplData.NextLessonInfo = courseStru.LessonPlayStruct{
				StartTime: int64(lessonDetail.StartTime),
				StopTime:  int64(lessonDetail.StopTime),
				LessonId:  int64(lessonDetail.LessonId),
			}

			// 判断条件
			if int64(lessonDetail.StopTime+1800) > currentTime {
				break
			}

			tmplData.LastLessonInfo = courseStru.LessonPlayStruct{
				StartTime: int64(lessonDetail.StartTime),
				StopTime:  int64(lessonDetail.StopTime),
				LessonId:  int64(lessonDetail.LessonId),
			}
		}
		resData[courseId] = tmplData
	}

	courseData.nextAndLastLesson = resData
	components.Debugf(ctx, "CourseListAndCardByYear initDataStepTwo,%v,%v,%+v", assistantUid, courseIds, resData)
	return courseData, nil
}

func (s courseService) formatCourseCardInfo(ctx *gin.Context, assistantUid, year int64, courseIds []int64, courseData *courseListAndCardByYearDataCollect) (res outputCourse.CourseListAndCardByYearResp, err error) {
	res = outputCourse.CourseListAndCardByYearResp{CourseList: []outputCourse.CourseListAndCardByYearItem{}}

	if courseData == nil {
		err = components.ErrorSystemError
		return
	}

	// 年级关联学部
	stageMap := courseData.stageMap

	// 年级名称
	gradeMap := define.GetGradeStage()

	// 学科名称
	subjectMap := courseData.subjectMap

	seasonName := coursebase.GetLearnSeasonIdNameFullMap(ctx)
	if len(seasonName) == 0 {
		seasonName = define.LearnSeasonNameMap
	}

	courseListArr := make([]outputCourse.CourseInfoCardItem, 0)

	for _, courseId := range courseIds {
		baseInfo, ok := courseData.baseCourseInfo[courseId]
		if !ok {
			zlog.Warnf(ctx, "formatCourseCardInfo skip in step 1,%v", courseId)
			continue
		} else {
			if baseInfo.Year != year {
				zlog.Warnf(ctx, "formatCourseCardInfo skip in step 2,%v", courseId)
				continue
			}
		}

		courseStatus := s.getCourseStatus(baseInfo)
		courseCard := outputCourse.CourseInfoCardItem{
			AssistantUid:          assistantUid,
			CourseID:              courseId,
			CourseName:            baseInfo.CourseName,
			CourseStartTime:       baseInfo.FirstLessonTime,
			CourseBeginTime:       baseInfo.StartTime,
			CourseFinishTime:      baseInfo.StopTime,
			MainGradeID:           baseInfo.MainGradeId,
			OnlineFormatTimeAll:   courseData.courseSkuFormatInfo[courseId].OnlineFormatTimeAll,
			NewCourseType:         baseInfo.NewCourseType,
			LearnSeason:           s.getLearnSeasonStr(baseInfo.LearnSeason, seasonName),
			Period:                baseInfo.LearnSeason,
			Department:            stageMap[baseInfo.MainGradeId],
			DepartmentName:        gradeMap[int(stageMap[baseInfo.MainGradeId])],
			CourseStatusWord:      components.GetCourseStatusMap()[courseStatus],
			Season:                s.getSeasonStr(baseInfo.Season, seasonName),
			CourseStatus:          int64(courseStatus),
			CoursePriceTag:        courseData.towerCourseInfo[courseId].CoursePriceTag,
			MainSubjectID:         baseInfo.MainSubjectId,
			MainSubjectName:       subjectMap[baseInfo.MainSubjectId],
			TransTimeStart:        courseData.transTimeInfo[courseId].TransTimeStart,
			CourseExpireTimeStart: courseData.transTimeInfo[courseId].CourseExpireTimeStart,
			ExpireTime:            courseData.expireInfo[courseId].ExpireTime,
			TeacherName:           strings.Join(courseData.courseSkuFormatInfo[courseId].TeacherNameList, ","),
			ServiceType:           int64(s.getServiceType(courseId, courseData.expireInfo)),
			CourseStatusType:      int64(s.getCourseStatusType(courseId, courseData.expireInfo)),
			LessonTime:            s.getLessonTime(courseData.nextAndLastLesson[courseId]),
			LessonID:              courseData.nextAndLastLesson[courseId].NextLessonInfo.LessonId,
			CourseTimeNew:         s.getCourseTimeNew(courseData.courseSkuFormatInfo[courseId]),
			SurveyType:            s.getSurveyType(ctx, courseId, courseData.courseSurveyNetInfo),
			OrderSurveyID:         courseData.courseSurveyNetInfo[courseId].OrderSurveyId,
			NeedSurveyID:          courseData.courseSurveyNetInfo[courseId].NeedSurveyId,
			TeacherNameList:       courseData.courseSkuFormatInfo[courseId].TeacherNameList,
		}

		// 转化期/服务期
		periodData := s.getPeriodType(courseData.transTimeInfo[courseId].TransTimeStart,
			courseData.expireInfo[courseId].ExpireTime, courseData.towerCourseInfo[courseId].SaleMode)
		if periodData[0] != "" {
			courseCard.PeriodType = periodData[0]
		}
		if periodData[1] != "" {
			courseCard.PeriodTime = periodData[1]
		}

		// 学生总数
		courseCard.TotalStudent = int64(courseData.leadsCountInfo[courseId].Total)
		courseListArr = append(courseListArr, courseCard)
	}

	// 排序
	sort.SliceStable(courseListArr, func(i, j int) bool {
		if courseListArr[i].CourseStartTime == courseListArr[j].CourseStartTime {
			return courseListArr[i].CourseID > courseListArr[j].CourseID
		}
		return courseListArr[i].CourseStartTime > courseListArr[j].CourseStartTime
	})

	// 按照服务状态分组
	inServiceCourse := outputCourse.CourseListAndCardByYearItem{
		ServiceTypeValue: components.ServiceTypeIn,
		ServiceTypeLabel: components.GetServiceTypeMap()[components.ServiceTypeIn],
		List:             make([]outputCourse.CourseInfoCardItem, 0),
	}
	beforeServiceCourse := outputCourse.CourseListAndCardByYearItem{
		ServiceTypeValue: components.ServiceTypeBefore,
		ServiceTypeLabel: components.GetServiceTypeMap()[components.ServiceTypeBefore],
		List:             make([]outputCourse.CourseInfoCardItem, 0),
	}
	afterServiceCourse := outputCourse.CourseListAndCardByYearItem{
		ServiceTypeValue: components.ServiceTypeAfter,
		ServiceTypeLabel: components.GetServiceTypeMap()[components.ServiceTypeAfter],
		List:             make([]outputCourse.CourseInfoCardItem, 0),
	}
	defaultServiceCourse := outputCourse.CourseListAndCardByYearItem{
		ServiceTypeValue: components.ServiceTypeCourseList,
		ServiceTypeLabel: components.GetServiceTypeMap()[components.ServiceTypeCourseList],
		List:             make([]outputCourse.CourseInfoCardItem, 0),
	}

	for _, item := range courseListArr {
		switch item.ServiceType {
		case components.ServiceTypeIn:
			inServiceCourse.List = append(inServiceCourse.List, item)
		case components.ServiceTypeBefore:
			beforeServiceCourse.List = append(beforeServiceCourse.List, item)
		case components.ServiceTypeAfter:
			afterServiceCourse.List = append(afterServiceCourse.List, item)
		case components.ServiceTypeCourseList:
			defaultServiceCourse.List = append(defaultServiceCourse.List, item)
		default:
		}
	}

	// 服务中
	if len(inServiceCourse.List) > 0 {
		res.CourseList = append(res.CourseList, inServiceCourse)
	}
	// 待服务
	if len(beforeServiceCourse.List) > 0 {
		res.CourseList = append(res.CourseList, beforeServiceCourse)
	}
	// 服务结束
	if len(afterServiceCourse.List) > 0 {
		res.CourseList = append(res.CourseList, afterServiceCourse)
	}
	// 默认课程列表
	if len(defaultServiceCourse.List) > 0 {
		res.CourseList = append(res.CourseList, defaultServiceCourse)
	}

	return
}

func (s courseService) getSurveyType(ctx *gin.Context, courseId int64, data map[int64]courseStru.SurveyTypeAndId) []int64 {
	if su, ok := data[courseId]; !ok {
		return make([]int64, 0)
	} else {
		return su.SurveyType
	}
}

func (s courseService) getPeriodType(transTimeStart, expireTime, saleMode int64) []string {

	if saleMode == components.FuDaoCourse {
		return []string{"", ""}
	}
	// lpc 展示
	now := time.Now().Unix()
	// 服务期
	if transTimeStart > 0 && now < transTimeStart {
		timeDur := transTimeStart - now
		return []string{"服务期", "距结束" + components.Util.FormatRemainTimeTSF(timeDur)}
	} else if expireTime > 0 && expireTime > now {
		timeDur := expireTime - now
		return []string{"转化期", "距结束" + components.Util.FormatRemainTimeTSF(timeDur)}
	}
	return []string{"", ""}
}

func (s courseService) getCourseTimeNew(skuInfo moat.FormatSkuInfos) string {
	parts := strings.Split(skuInfo.OnlineFormatTimeAll, " ")
	if len(parts) > 3 {
		parts = parts[:len(parts)-3]
	}
	courseTimeNew := strings.Join(parts, " ")
	return courseTimeNew
}

func (s courseService) getLessonTime(lessonInfo courseStru.NextLastLessonStruct) string {
	// 之前的格式就有问题，其实没用，先放在这吧
	timeStr := "-" + time.Unix(lessonInfo.NextLessonInfo.StopTime, 0).Format("2006年01月02日 15:04")
	return timeStr
}

func (s courseService) getLearnSeasonStr(period int64, seasonName map[int]string) string {
	str := seasonName[int(period)]
	return strings.Replace(str, "_", "", -1)
}

func (s courseService) getSeasonStr(period int64, seasonName map[int]string) string {
	str := seasonName[int(period)]
	if len(str) == 0 {
		return str
	}
	runes := []rune(str)
	return string(runes[0])
}

func (s courseService) getCourseStatus(courseData dal.CourseInfo) int {
	firstLessonTime := courseData.FirstLessonTime
	lastLessonStopTime := courseData.LastLessonStopTime
	now := time.Now().Unix()

	if now < firstLessonTime {
		return components.CourseStatusUnstart
	}

	if now > firstLessonTime && now < lastLessonStopTime {
		return components.CourseStatusIng
	}

	if now > lastLessonStopTime {
		return components.CourseStatusEnd
	}

	return components.CourseStatusUnstart
}

func (s courseService) getServiceType(courseId int64, info map[int64]baseTower.CourseExpireTime) int {
	now := time.Now().Unix()
	data, ok := info[courseId]
	if !ok {
		return components.ServiceTypeCourseList
	}
	start := data.ExpireTimeStart
	end := data.ExpireTime

	if now < start {
		return components.ServiceTypeBefore
	} else if now < end && now > start {
		return components.ServiceTypeIn
	} else {
		return components.ServiceTypeAfter
	}
}

func (s courseService) getCourseStatusType(courseId int64, info map[int64]baseTower.CourseExpireTime) int {
	now := time.Now().Unix()
	data, ok := info[courseId]
	if !ok {
		return components.ServiceTypeCourseList
	}

	start := data.ExpireTimeStart
	end := data.ExpireTime + 7*24*60*60

	if now < start {
		return components.ServiceTypeBefore
	} else if now < end && now > start {
		return components.ServiceTypeIn
	} else {
		return components.ServiceTypeAfter
	}
}

func (s courseService) GetLessonPlayInfoList(ctx *gin.Context, assistantUid int64, courseIds []int64) (resp map[int64][]genke.GetLinkRet, err error) {
	isTraining := 0
	// 是否是培训环境
	if utils.IsTrainEnv(ctx) {
		isTraining = 1
	}

	resp, err = genke.NewClient().GetLinks(ctx, genke.GetLinkParam{
		CourseIds:    courseIds,
		AssistantUid: assistantUid,
		IsTraining:   isTraining,
	})
	if err != nil {
		return nil, err
	}

	return
}

func (s courseService) AllowAutoCallAndMessage(ctx *gin.Context, courseId int64) (resp *courseStru.AllowAutoCallAndMessageResp, err error) {
	resp = &courseStru.AllowAutoCallAndMessageResp{
		Allow: consts.NotAllowAutoCall,
	}
	courseInfos, err := dal.GetCourseBaseByCourseIds(ctx, []int64{courseId}, []string{"courseId", "newCourseType"})
	if err != nil {
		return
	}

	//imc不允许1v1群发和自动外呼
	if info, ok := courseInfos[courseId]; ok {
		if components.Array.InArrayInt(int(info.NewCourseType), consts.NewCourseTypeImcMap) {
			return
		}
	}

	// 其他全部放开
	resp.Allow = consts.AllowAutoCall
	return
}

func (s courseService) GetSipInfo(ctx *gin.Context, courseId int64, businessType string, deviceUid, personUid int64) (resp *courseStru.GetSipInfoResp, err error) {
	resp = &courseStru.GetSipInfoResp{
		AgentId: "",
		Pwd:     "",
		URL:     "",
	}

	courseInfos, err := dal.GetCourseBaseByCourseIds(ctx, []int64{courseId}, []string{"courseId", "serviceInfo"})
	if err != nil {
		return
	}

	businessLine := consts.LINE_FUDAO
	if dal.IsLpcByCourse(ctx, courseInfos[courseId]) {
		businessLine = consts.LINE_LPC
	}

	if businessType == consts.BUSINESS_TYPE_FOR_PUBLIC_SEA {
		businessLine = consts.LINE_LPC
	}

	sipInfo, err := touchmis.GetSipAgentInfo(ctx, touchmis.TouchSipAgentInfoRep{
		CourseId:     courseId,
		DeviceUid:    deviceUid,
		PersonUid:    personUid,
		BusinessType: businessType,
		BusinessLine: businessLine,
	})
	if err != nil {
		return nil, err
	}

	resp.AgentId = sipInfo.AgentId
	resp.Pwd = sipInfo.Pwd
	resp.URL = sipInfo.URL

	return
}
