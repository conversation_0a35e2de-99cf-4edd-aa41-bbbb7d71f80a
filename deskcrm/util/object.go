package util

import (
	"encoding/json"
	"reflect"
)

// CopyObjFields 同名字段赋值, 将源结构体的字段复制到目标结构体
func CopyObjFields(src, dst interface{}) {
	srcValue := reflect.ValueOf(src)
	dstValue := reflect.ValueOf(dst).Elem()

	if srcValue.Kind() == reflect.Ptr {
		srcValue = srcValue.Elem()
	}

	srcType := srcValue.Type()

	for i := 0; i < srcValue.NumField(); i++ {
		srcField := srcValue.Field(i)
		srcFieldName := srcType.Field(i).Name

		dstField := dstValue.FieldByName(srcFieldName)
		if dstField.IsValid() && dstField.CanSet() && dstField.Type() == srcField.Type() {
			dstField.Set(srcField)
		}
	}
}

// 获取结构体的 Json 字段名
func GetJsonFieldNames(s interface{}) []string {
	t := reflect.TypeOf(s)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return nil
	}

	var fieldNames []string
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		jsonTag := field.Tag.Get("json")
		if jsonTag != "" {
			// 处理可能存在的选项，如 omitempty
			if commaIndex := index(jsonTag, ','); commaIndex != -1 {
				jsonTag = jsonTag[:commaIndex]
			}
			fieldNames = append(fieldNames, jsonTag)
		}
	}
	return fieldNames
}

// 辅助函数，模拟 strings.Index 功能
func index(s string, sep rune) int {
	for i, r := range s {
		if r == sep {
			return i
		}
	}
	return -1
}

// StructToMap 将结构体转换为map[string]interface{}
func StructToMap(obj interface{}) (map[string]interface{}, error) {
	// 1. 结构体转 JSON
	data, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}

	// 2. JSON 转 map
	var result map[string]any
	err = json.Unmarshal(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
